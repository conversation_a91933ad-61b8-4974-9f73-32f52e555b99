package com.ruoyi.ffsafe.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import com.ruoyi.common.utils.DateUtils;
import java.util.List;

/**
 * 流量风险资产API响应结果类
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlowRiskAssetsResult {
    
    @JsonProperty("msg")
    private String msg;
    
    @JsonProperty("alert")
    private List<FlowRiskAssetDetail> alert;
    
    @JsonProperty("total")
    private Integer total;
    
    @Data
    public static class FlowRiskAssetDetail {
        @JsonProperty("risk_assets")
        private String riskAssets;
        
        @JsonProperty("risk_type")
        private String riskType;
        
        @JsonProperty("risk_info")
        private String riskInfo;
        
        @JsonProperty("engine_name")
        private String engineName;
        
        @JsonProperty("start_time")
        private String startTime;
        
        @JsonProperty("update_time")
        private String updateTime;
        
        @JsonProperty("state")
        private String state;
        
        public FfsafeFlowRiskAssets toEntity() {
            FfsafeFlowRiskAssets entity = new FfsafeFlowRiskAssets();
            entity.setRiskAssets(this.riskAssets);
            entity.setRiskType(this.riskType);
            entity.setRiskInfo(this.riskInfo);
            entity.setEngineName(this.engineName);
            entity.setStartTime(DateUtils.parseDate(this.startTime));
            entity.setUpdateTime(DateUtils.parseDate(this.updateTime));
            // 第三方状态不直接使用，本地初始化为未处置
            entity.setHandleState(0);
            return entity;
        }
    }
} 