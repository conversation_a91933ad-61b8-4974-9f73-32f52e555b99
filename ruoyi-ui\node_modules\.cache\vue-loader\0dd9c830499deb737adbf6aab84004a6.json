{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\layout\\components\\Sidebar\\index.vue?vue&type=style&index=0&id=33ec43fc&scoped=true&lang=scss", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1756090207111}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZWwtbWVudSB7CiAgYmFja2dyb3VuZC1jb2xvcjogdW5zZXQ7CiAgOjp2LWRlZXAgLmVsLXN1Ym1lbnVfX3RpdGxlIGkgewogICAgZm9udC13ZWlnaHQ6IDcwMDsKICB9Cn0KLnNpZGViYXItYm90dG9tIHsKICB3aWR0aDogMTkwcHg7CiAgaGVpZ2h0OiA3MnB4OwogIHBvc2l0aW9uOiBmaXhlZDsKICBib3R0b206IDA7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjN2Y3ZjdmMjQ7CiAgLnRleHR7CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICBwYWRkaW5nOiAycHggMnB4IDJweCAycHg7CiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogICAgd2lkdGg6IDEwMCU7CiAgICBoZWlnaHQ6IDU0cHg7CiAgICBjb2xvcjogcmdiYSgxMjcsIDEyNywgMTI3LCAwLjQ5ODAzOTIxNTY4NjI3NDUpOwogICAgZm9udC1zaXplOiAxMnB4OwogICAgd29yZC13cmFwOiBicmVhay13b3JkOwogICAgdGV4dC10cmFuc2Zvcm06IG5vbmU7CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\n  <div :class=\"{'has-logo':showLogo}\">\n<!--       :style=\"{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground,-->\n<!--           background: settings.sideTheme === 'theme-dark' ? '' : variables.menuGradient }\">-->\n<!--    <logo v-if=\"showLogo\" :collapse=\"isCollapse\"/>-->\n    <el-scrollbar :class=\"settings.sideTheme\" wrap-class=\"scrollbar-wrapper\">\n      <!--          :background-color=\"settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground\"-->\n      <el-menu\n        :default-active=\"activeMenu\"\n        :collapse=\"isCollapse\"\n        :unique-opened=\"true\"\n        :collapse-transition=\"false\"\n        mode=\"vertical\"\n      >\n        <sidebar-item\n          v-for=\"(route, index) in sidebarRouters\"\n          :key=\"route.path  + index\"\n          :item=\"route\"\n          :base-path=\"route.path\"\n        />\n      </el-menu>\n    </el-scrollbar>\n    <div class=\"sidebar-bottom\" v-if=\"!isCollapse\">\n      <div class=\"text\">\n        <p><span>当前版本：2.5.0</span><span style=\"display: none;\">Date: 2025-04-27 17:24 SvnVersion: $Revision: 2884 $</span></p>\n        <p><span>技术支持：安盟科技</span></p>\n        <p><span>联系方式：4001-110-9963</span></p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {mapGetters, mapState} from \"vuex\";\nimport Logo from \"./Logo\";\nimport SidebarItem from \"./SidebarItem\";\nimport variables from \"@/assets/styles/variables.scss\";\n\nexport default {\n  components: {SidebarItem, Logo},\n  mounted() {},\n  computed: {\n    ...mapState([\"settings\"]),\n    ...mapGetters([\"sidebarRouters\", \"sidebar\"]),\n    activeMenu() {\n      const route = this.$route;\n      const {meta, path} = route;\n      // if set path, the sidebar will highlight the path you set\n      if (meta.activeMenu) {\n        return meta.activeMenu;\n      }\n      let activeMenu\n      const parts = path.split('/');\n      if (parts.length === 4 && parts[1] && parts[2] && parts[3]) {\n        activeMenu = `/${parts[1]}/${parts[2]}`;\n      }else{\n        activeMenu =  path;\n      }\n      return activeMenu\n    },\n    showLogo() {\n      return this.$store.state.settings.sidebarLogo;\n    },\n    variables() {\n      return variables;\n    },\n    // 侧边栏折叠状态\n    isCollapse() {\n      return !this.sidebar.opened;\n    }\n  }\n};\n</script>\n<style scoped lang=\"scss\">\n.el-menu {\n  background-color: unset;\n  ::v-deep .el-submenu__title i {\n    font-weight: 700;\n  }\n}\n.sidebar-bottom {\n  width: 190px;\n  height: 72px;\n  position: fixed;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  border-top: 1px solid #7f7f7f24;\n  .text{\n    position: absolute;\n    text-align: center;\n    padding: 2px 2px 2px 2px;\n    box-sizing: border-box;\n    width: 100%;\n    height: 54px;\n    color: rgba(127, 127, 127, 0.4980392156862745);\n    font-size: 12px;\n    word-wrap: break-word;\n    text-transform: none;\n  }\n}\n</style>\n"]}]}