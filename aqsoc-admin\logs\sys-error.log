2025-08-25 00:00:00.073 [quartzScheduler_Worker-6] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 当前任务正在扫描中, 请忽重复调度.
	at com.ruoyi.ffsafe.scantaskapi.event.WebVulnScan.scan(WebVulnScan.java:57)
	... 10 common frames omitted
2025-08-25 00:00:00.089 [quartzScheduler_Worker-5] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 当前任务正在扫描中, 请忽重复调度.
	at com.ruoyi.ffsafe.scantaskapi.event.WebVulnScan.scan(WebVulnScan.java:57)
	... 10 common frames omitted
2025-08-25 00:00:00.132 [quartzScheduler_Worker-3] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 当前任务正在扫描中, 请忽重复调度.
	at com.ruoyi.ffsafe.scantaskapi.event.HostVulnScan.scan(HostVulnScan.java:68)
	... 10 common frames omitted
2025-08-25 00:00:00.133 [quartzScheduler_Worker-1] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 当前任务正在扫描中, 请忽重复调度.
	at com.ruoyi.ffsafe.scantaskapi.event.HostVulnScan.scan(HostVulnScan.java:68)
	... 10 common frames omitted
2025-08-25 00:00:00.978 [quartzScheduler_Worker-2] ERROR c.r.f.s.s.i.SyncFfHostEdrServiceImpl - [syncFfHostEdrDetails,274] - host_detail_by_serial 接口调用错误: Unrecognized field "jar_details" (class com.ruoyi.ffsafe.scantaskapi.domain.EdrDetailsResult), not marked as ignorable (4 known properties: "service_details", "process_details", "hardware_details", "software_details"])
 at [Source: (StringReader); line: 1, column: 93524] (through reference chain: com.ruoyi.ffsafe.scantaskapi.domain.EdrDetailsResult["jar_details"])
2025-08-25 00:00:00.992 [quartzScheduler_Worker-8] ERROR c.r.f.s.s.i.SyncFfHostEdrServiceImpl - [syncFfHostEdrDetails,274] - host_detail_by_serial 接口调用错误: Unrecognized field "jar_details" (class com.ruoyi.ffsafe.scantaskapi.domain.EdrDetailsResult), not marked as ignorable (4 known properties: "service_details", "process_details", "hardware_details", "software_details"])
 at [Source: (StringReader); line: 1, column: 93524] (through reference chain: com.ruoyi.ffsafe.scantaskapi.domain.EdrDetailsResult["jar_details"])
2025-08-25 00:00:01.007 [quartzScheduler_Worker-2] ERROR c.r.m.c.j.FfHostEdrSyncTask - [lambda$syncFfHostEdr$0,60] - host_detail_by_serial 接口调用错误: Unrecognized field "jar_details" (class com.ruoyi.ffsafe.scantaskapi.domain.EdrDetailsResult), not marked as ignorable (4 known properties: "service_details", "process_details", "hardware_details", "software_details"])
 at [Source: (StringReader); line: 1, column: 93524] (through reference chain: com.ruoyi.ffsafe.scantaskapi.domain.EdrDetailsResult["jar_details"])
2025-08-25 00:00:01.019 [quartzScheduler_Worker-8] ERROR c.r.m.c.j.FfHostEdrSyncTask - [lambda$syncFfHostEdr$0,60] - host_detail_by_serial 接口调用错误: Unrecognized field "jar_details" (class com.ruoyi.ffsafe.scantaskapi.domain.EdrDetailsResult), not marked as ignorable (4 known properties: "service_details", "process_details", "hardware_details", "software_details"])
 at [Source: (StringReader); line: 1, column: 93524] (through reference chain: com.ruoyi.ffsafe.scantaskapi.domain.EdrDetailsResult["jar_details"])
2025-08-25 00:00:05.159 [taskScheduler-18] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getFirewallList,458] - 图幻-----获取防火墙列表-----失败： SSLHandshakeException: Remote host terminated the handshake
2025-08-25 00:30:05.007 [taskScheduler-26] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getNatList,492] - 图幻-----获取防火墙NAT列表--失败：SSLHandshakeException: Remote host terminated the handshake
2025-08-25 04:48:02.330 [pool-87-thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1453 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-25 04:48:02.409 [pool-87-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1559 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-25 04:48:02.766 [pool-87-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1942 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) ) and ta.handle_state=0["**************","***************","***************","**************","***************","***************"]
2025-08-25 04:48:03.238 [pool-87-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2361 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         ) ) and ta.handle_state=0["***************","***************","***************","***************"]
2025-08-25 09:03:54.958 [taskScheduler-24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1134 millis. select id, equipment_name, hash_value, equipment_number, service_platform_key, service_platform_ip, server_port, registration_time from tbl_equipment_info[]
2025-08-25 09:48:09.602 [pool-93-thread-4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3320 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         ) ) and ta.handle_state=0["***************","***************","***************","***************"]
2025-08-25 09:48:09.647 [pool-93-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3429 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-25 09:48:09.704 [pool-93-thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3484 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-25 09:48:09.836 [pool-93-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3618 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) ) and ta.handle_state=0["**************","***************","***************","**************","***************","***************"]
2025-08-25 09:49:02.652 [hutool-cron-38] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 10:25:40.568 [taskScheduler-19] ERROR c.a.d.p.DruidPooledStatement - [errorCheck,370] - CommunicationsException, druid version 1.2.11, jdbcUrl : jdbc:mysql://***************:3306/aqsoc?useUnicode=true&characterEncoding=utf8&nullCatalogMeansCurrent=true&allowLoadLocalInfile=true, testWhileIdle true, idle millis 32629, minIdle 10, poolingCount 1, timeBetweenEvictionRunsMillis 60000, lastValidIdleMillis 32629, driver com.mysql.cj.jdbc.Driver, exceptionSorter com.alibaba.druid.pool.vendor.MySqlExceptionSorter
2025-08-25 10:25:40.706 [taskScheduler-19] ERROR c.a.d.p.DruidDataSource - [handleFatalError,1867] - {conn-10178} discard
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 32,430 milliseconds ago. The last packet sent successfully to the server was 32,430 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:152)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at sun.reflect.GeneratedMethodAccessor149.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy154.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.GeneratedMethodAccessor181.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy152.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy151.query(Unknown Source)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:132)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy151.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor222.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy148.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy295.selectTblDeviceConfigList(Unknown Source)
	at com.ruoyi.ffsafe.api.service.impl.TblDeviceConfigServiceImpl.selectTblDeviceConfigList(TblDeviceConfigServiceImpl.java:76)
	at com.ruoyi.safe.task.FfIpFilterBlockingTask.sync(FfIpFilterBlockingTask.java:61)
	at sun.reflect.GeneratedMethodAccessor865.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 32,430 milliseconds ago. The last packet sent successfully to the server was 32,430 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:761)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:700)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:1051)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:665)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:893)
	... 56 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:475)
	at sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:469)
	at sun.security.ssl.SSLSocketInputRecord.bytesInCompletePacket(SSLSocketInputRecord.java:69)
	at sun.security.ssl.SSLSocketImpl.readApplicationRecord(SSLSocketImpl.java:1271)
	at sun.security.ssl.SSLSocketImpl.access$300(SSLSocketImpl.java:76)
	at sun.security.ssl.SSLSocketImpl$AppInputStream.read(SSLSocketImpl.java:948)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 61 common frames omitted
2025-08-25 10:25:41.041 [taskScheduler-19] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - [handleError,95] - Unexpected error occurred in scheduled task
org.springframework.dao.RecoverableDataAccessException: 
### Error querying database.  Cause: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 32,430 milliseconds ago. The last packet sent successfully to the server was 32,430 milliseconds ago.
### The error may exist in file [E:\wsh\augment_workspace\aqsoc-main\aqsoc-monitor\target\classes\mapper\ffsafe\TblDeviceConfigMapper.xml]
### The error may involve com.ruoyi.ffsafe.api.mapper.TblDeviceConfigMapper.selectTblDeviceConfigList-Inline
### The error occurred while setting parameters
### SQL: select id, device_name, device_ip, device_params, create_time, create_by, update_time, update_by, status,filter_log_last_time,alarm_detail_last_time,risk_asset_last_time from tbl_device_config                WHERE  `status` = ?
### Cause: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 32,430 milliseconds ago. The last packet sent successfully to the server was 32,430 milliseconds ago.
; Communications link failure

The last packet successfully received from the server was 32,430 milliseconds ago. The last packet sent successfully to the server was 32,430 milliseconds ago.; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 32,430 milliseconds ago. The last packet sent successfully to the server was 32,430 milliseconds ago.
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:100)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:79)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy148.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy295.selectTblDeviceConfigList(Unknown Source)
	at com.ruoyi.ffsafe.api.service.impl.TblDeviceConfigServiceImpl.selectTblDeviceConfigList(TblDeviceConfigServiceImpl.java:76)
	at com.ruoyi.safe.task.FfIpFilterBlockingTask.sync(FfIpFilterBlockingTask.java:61)
	at sun.reflect.GeneratedMethodAccessor865.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 32,430 milliseconds ago. The last packet sent successfully to the server was 32,430 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:152)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at sun.reflect.GeneratedMethodAccessor149.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy154.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.GeneratedMethodAccessor181.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy152.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy151.query(Unknown Source)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:132)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy151.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor222.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 21 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 32,430 milliseconds ago. The last packet sent successfully to the server was 32,430 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:761)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:700)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:1051)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:665)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:893)
	... 56 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:475)
	at sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:469)
	at sun.security.ssl.SSLSocketInputRecord.bytesInCompletePacket(SSLSocketInputRecord.java:69)
	at sun.security.ssl.SSLSocketImpl.readApplicationRecord(SSLSocketImpl.java:1271)
	at sun.security.ssl.SSLSocketImpl.access$300(SSLSocketImpl.java:76)
	at sun.security.ssl.SSLSocketImpl$AppInputStream.read(SSLSocketImpl.java:948)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 61 common frames omitted
2025-08-25 10:25:42.575 [Thread-9] ERROR c.a.d.p.DruidPooledStatement - [errorCheck,370] - CommunicationsException, druid version 1.2.11, jdbcUrl : jdbc:mysql://***************:3306/aqsoc?useUnicode=true&characterEncoding=utf8&nullCatalogMeansCurrent=true&allowLoadLocalInfile=true, testWhileIdle true, idle millis 34683, minIdle 10, poolingCount 2, timeBetweenEvictionRunsMillis 60000, lastValidIdleMillis 34683, driver com.mysql.cj.jdbc.Driver, exceptionSorter com.alibaba.druid.pool.vendor.MySqlExceptionSorter
2025-08-25 10:25:42.576 [Thread-9] ERROR c.a.d.p.DruidDataSource - [handleFatalError,1867] - {conn-10175} discard
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 34,683 milliseconds ago. The last packet sent successfully to the server was 34,684 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:152)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at sun.reflect.GeneratedMethodAccessor149.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy154.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.GeneratedMethodAccessor181.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy152.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy151.query(Unknown Source)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:132)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy151.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor222.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy148.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy238.selectDataDictChangeList(Unknown Source)
	at com.ruoyi.datainterface.service.impl.DataDictChangeServiceImpl.selectDataDictChangeList(DataDictChangeServiceImpl.java:49)
	at com.ruoyi.datainterface.event.InterfaceEventMonitor.updateDict(InterfaceEventMonitor.java:232)
	at com.ruoyi.datainterface.event.InterfaceEventMonitor.run(InterfaceEventMonitor.java:343)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 34,683 milliseconds ago. The last packet sent successfully to the server was 34,684 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:761)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:700)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:1051)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:665)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:893)
	... 45 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:475)
	at sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:469)
	at sun.security.ssl.SSLSocketInputRecord.bytesInCompletePacket(SSLSocketInputRecord.java:69)
	at sun.security.ssl.SSLSocketImpl.readApplicationRecord(SSLSocketImpl.java:1271)
	at sun.security.ssl.SSLSocketImpl.access$300(SSLSocketImpl.java:76)
	at sun.security.ssl.SSLSocketImpl$AppInputStream.read(SSLSocketImpl.java:948)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 50 common frames omitted
2025-08-25 10:25:42.632 [Thread-9] ERROR c.r.d.e.InterfaceEventMonitor - [run,349] - InterfaceEventMonitor thread error: 
### Error querying database.  Cause: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 34,683 milliseconds ago. The last packet sent successfully to the server was 34,684 milliseconds ago.
### The error may exist in file [E:\wsh\augment_workspace\aqsoc-main\aqsoc-monitor\target\classes\mapper\datainterface\DataDictChangeMapper.xml]
### The error may involve com.ruoyi.datainterface.mapper.DataDictChangeMapper.selectDataDictChangeList-Inline
### The error occurred while setting parameters
### SQL: select id, dict_name, dict_array, dict_desc, dict_config from data_dict_change
### Cause: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 34,683 milliseconds ago. The last packet sent successfully to the server was 34,684 milliseconds ago.
; Communications link failure

The last packet successfully received from the server was 34,683 milliseconds ago. The last packet sent successfully to the server was 34,684 milliseconds ago.; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 34,683 milliseconds ago. The last packet sent successfully to the server was 34,684 milliseconds ago.
2025-08-25 10:26:58.581 [http-nio-8080-exec-97] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - [log,175] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "%2e"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:535)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:505)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:196)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 10:31:04.842 [taskScheduler-4] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - [handleError,95] - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: cn.hutool.core.io.IORuntimeException: SSLHandshakeException: Remote host terminated the handshake
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:273)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:280)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1643)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: cn.hutool.core.io.IORuntimeException: SSLHandshakeException: Remote host terminated the handshake
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1350)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1188)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1051)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1027)
	at com.ruoyi.ffsafe.component.FFSafeRequestComponent.execRequest(FFSafeRequestComponent.java:86)
	at com.ruoyi.ffsafe.component.FFSafeRequestComponent.sendPostRequest(FFSafeRequestComponent.java:55)
	at com.ruoyi.safe.task.FfIpFilterBlockingTask.lambda$null$1(FfIpFilterBlockingTask.java:70)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	... 3 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
	at sun.security.ssl.SSLSocketImpl.handleEOF(SSLSocketImpl.java:1511)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1328)
	at sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1233)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:417)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:558)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:201)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1354)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1329)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:264)
	at cn.hutool.http.HttpConnection.getOutputStream(HttpConnection.java:458)
	at cn.hutool.http.HttpRequest.sendFormUrlEncoded(HttpRequest.java:1367)
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1342)
	... 10 common frames omitted
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:480)
	at sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:469)
	at sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:159)
	at sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1320)
	... 21 common frames omitted
2025-08-25 10:31:21.535 [taskScheduler-4] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - [handleError,95] - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: cn.hutool.core.io.IORuntimeException: SSLHandshakeException: Remote host terminated the handshake
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:273)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:280)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1643)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: cn.hutool.core.io.IORuntimeException: SSLHandshakeException: Remote host terminated the handshake
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1350)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1188)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1051)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1027)
	at com.ruoyi.ffsafe.component.FFSafeRequestComponent.execRequest(FFSafeRequestComponent.java:86)
	at com.ruoyi.ffsafe.component.FFSafeRequestComponent.sendPostRequest(FFSafeRequestComponent.java:55)
	at com.ruoyi.safe.task.FfIpFilterBlockingTask.lambda$null$1(FfIpFilterBlockingTask.java:70)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	... 3 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
	at sun.security.ssl.SSLSocketImpl.handleEOF(SSLSocketImpl.java:1511)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1328)
	at sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1233)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:417)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:558)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:201)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1354)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1329)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:264)
	at cn.hutool.http.HttpConnection.getOutputStream(HttpConnection.java:458)
	at cn.hutool.http.HttpRequest.sendFormUrlEncoded(HttpRequest.java:1367)
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1342)
	... 10 common frames omitted
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:480)
	at sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:469)
	at sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:159)
	at sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1320)
	... 21 common frames omitted
2025-08-25 10:31:38.362 [hutool-cron-39] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 10:31:54.104 [hutool-cron-38] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 10:31:59.362 [taskScheduler-44] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - [handleError,95] - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: cn.hutool.core.io.IORuntimeException: SSLHandshakeException: Remote host terminated the handshake
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:273)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:280)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1643)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: cn.hutool.core.io.IORuntimeException: SSLHandshakeException: Remote host terminated the handshake
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1350)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1188)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1051)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1027)
	at com.ruoyi.ffsafe.component.FFSafeRequestComponent.execRequest(FFSafeRequestComponent.java:86)
	at com.ruoyi.ffsafe.component.FFSafeRequestComponent.sendPostRequest(FFSafeRequestComponent.java:55)
	at com.ruoyi.safe.task.FfIpFilterBlockingTask.lambda$null$1(FfIpFilterBlockingTask.java:70)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	... 3 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
	at sun.security.ssl.SSLSocketImpl.handleEOF(SSLSocketImpl.java:1511)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1328)
	at sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1233)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:417)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:558)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:201)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1354)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1329)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:264)
	at cn.hutool.http.HttpConnection.getOutputStream(HttpConnection.java:458)
	at cn.hutool.http.HttpRequest.sendFormUrlEncoded(HttpRequest.java:1367)
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1342)
	... 10 common frames omitted
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:480)
	at sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:469)
	at sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:159)
	at sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1320)
	... 21 common frames omitted
2025-08-25 10:32:17.813 [taskScheduler-35] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - [handleError,95] - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: cn.hutool.core.io.IORuntimeException: SSLHandshakeException: Remote host terminated the handshake
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:273)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:280)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1643)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: cn.hutool.core.io.IORuntimeException: SSLHandshakeException: Remote host terminated the handshake
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1350)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1188)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1051)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1027)
	at com.ruoyi.ffsafe.component.FFSafeRequestComponent.execRequest(FFSafeRequestComponent.java:86)
	at com.ruoyi.ffsafe.component.FFSafeRequestComponent.sendPostRequest(FFSafeRequestComponent.java:55)
	at com.ruoyi.safe.task.FfIpFilterBlockingTask.lambda$null$1(FfIpFilterBlockingTask.java:70)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	... 3 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
	at sun.security.ssl.SSLSocketImpl.handleEOF(SSLSocketImpl.java:1511)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1328)
	at sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1233)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:417)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:558)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:201)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1354)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1329)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:264)
	at cn.hutool.http.HttpConnection.getOutputStream(HttpConnection.java:458)
	at cn.hutool.http.HttpRequest.sendFormUrlEncoded(HttpRequest.java:1367)
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1342)
	... 10 common frames omitted
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:480)
	at sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:469)
	at sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:159)
	at sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1320)
	... 21 common frames omitted
2025-08-25 10:32:47.510 [taskScheduler-14] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - [handleError,95] - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: cn.hutool.core.io.IORuntimeException: SSLHandshakeException: Remote host terminated the handshake
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:273)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:280)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1643)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: cn.hutool.core.io.IORuntimeException: SSLHandshakeException: Remote host terminated the handshake
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1350)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1188)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1051)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1027)
	at com.ruoyi.ffsafe.component.FFSafeRequestComponent.execRequest(FFSafeRequestComponent.java:86)
	at com.ruoyi.ffsafe.component.FFSafeRequestComponent.sendPostRequest(FFSafeRequestComponent.java:55)
	at com.ruoyi.safe.task.FfIpFilterBlockingTask.lambda$null$1(FfIpFilterBlockingTask.java:70)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	... 3 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
	at sun.security.ssl.SSLSocketImpl.handleEOF(SSLSocketImpl.java:1511)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1328)
	at sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1233)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:417)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:558)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:201)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1354)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1329)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:264)
	at cn.hutool.http.HttpConnection.getOutputStream(HttpConnection.java:458)
	at cn.hutool.http.HttpRequest.sendFormUrlEncoded(HttpRequest.java:1367)
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1342)
	... 10 common frames omitted
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:480)
	at sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:469)
	at sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:159)
	at sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1320)
	... 21 common frames omitted
2025-08-25 10:32:49.353 [hutool-cron-39] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 10:35:00.963 [taskScheduler-34] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - [handleError,95] - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: cn.hutool.core.io.IORuntimeException: SSLHandshakeException: Remote host terminated the handshake
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:273)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:280)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1643)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: cn.hutool.core.io.IORuntimeException: SSLHandshakeException: Remote host terminated the handshake
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1350)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1188)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1051)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1027)
	at com.ruoyi.ffsafe.component.FFSafeRequestComponent.execRequest(FFSafeRequestComponent.java:86)
	at com.ruoyi.ffsafe.component.FFSafeRequestComponent.sendPostRequest(FFSafeRequestComponent.java:55)
	at com.ruoyi.safe.task.FfIpFilterBlockingTask.lambda$null$1(FfIpFilterBlockingTask.java:70)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	... 3 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
	at sun.security.ssl.SSLSocketImpl.handleEOF(SSLSocketImpl.java:1511)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1328)
	at sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1233)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:417)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:558)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:201)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1354)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1329)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:264)
	at cn.hutool.http.HttpConnection.getOutputStream(HttpConnection.java:458)
	at cn.hutool.http.HttpRequest.sendFormUrlEncoded(HttpRequest.java:1367)
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1342)
	... 10 common frames omitted
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:480)
	at sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:469)
	at sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:159)
	at sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1320)
	... 21 common frames omitted
2025-08-25 10:40:36.476 [http-nio-8080-exec-36] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1172 millis. select job_log_id, job_name, job_group, invoke_target, job_message, status, exception_info, create_time 
		from sys_job_log
     
		 WHERE  job_name like concat('%', ?, '%')
			
			
				AND job_group = ?
			
			
			
				AND invoke_target = ?
			
			order by create_time desc limit 1["A_测试主机漏扫汇总重复生成问题_A","ASSET_SCAN","HostVulnScan.scan('476','A_测试主机漏扫汇总重复生成问题_A|***************;**************|1|1|1')"]
2025-08-25 10:45:42.792 [async-task-pool21] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8740 millis. select id, ip, location, action, conditions, filter_src, create_time, release_time, remark,device_config_id from ffsafe_ipfilter_blocking
     
          
        order by create_time desc[]
2025-08-25 10:45:42.797 [async-task-pool100] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8754 millis. select id, ip, location, action, conditions, filter_src, create_time, release_time, remark,device_config_id from ffsafe_ipfilter_blocking
     
          
        order by create_time desc[]
2025-08-25 10:45:42.860 [Thread-9] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12519 millis. select id, config_name, array_name, config_desc, src_config, cmd_key, data_sample,predeal_sample, decode_sample, predeal_config, decode_config, correspond_config, status, create_by, create_time, update_by, update_time from data_interface_config
     
         WHERE  status = ? 
        ORDER BY create_time DESC[1]
2025-08-25 10:45:42.860 [http-nio-8080-exec-46] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6972 millis. SELECT count(0) FROM sys_job t1 LEFT JOIN sys_user su ON su.user_name = t1.create_by WHERE t1.is_del = 0 AND t1.job_group = ? AND t1.job_type = ?["ASSET_SCAN",1]
2025-08-25 10:51:58.358 [pool-5-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1000 millis. SELECT
        t1.id,
        t1.deduction_date,
        t1.deduction_type,
        t1.deduction_level,
        t1.deduction_score,
        t1.user_id,
        t1.department_id,
        t1.risk_type,
        t1.reference_id,
        t1.created_time,
        t1.created_by
        FROM
        (SELECT * FROM tbl_deduction_detail WHERE is_del = '1') t1
         
         
         
         
         WHERE  t1.deduction_type = ? 
        order by t1.deduction_date desc["主机漏洞"]
2025-08-25 10:52:03.499 [async-task-pool5] ERROR c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,509] - 处理第1页数据时发生异常，已达最大重试次数: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:987)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1974)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1701)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1679)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:330)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:355)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:324)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:322)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4593)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:447)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$1(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 10:52:03.603 [async-task-pool4] ERROR c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,509] - 处理第1页数据时发生异常，已达最大重试次数: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:987)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1974)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1701)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1679)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:330)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:355)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:324)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:322)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4593)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:447)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$1(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 10:52:04.145 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1772 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",32776,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","dC0BAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-23 06:04:02",4]
2025-08-25 10:52:04.474 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1311 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",32776,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","dC0BAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-23 06:04:02",1]
2025-08-25 10:52:06.887 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1894 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",39497,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","AE8BAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAAcAAE=","[]","2025-08-23 06:02:51",4]
2025-08-25 10:52:07.030 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1740 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",39497,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","AE8BAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAAcAAE=","[]","2025-08-23 06:02:51",1]
2025-08-25 10:52:09.431 [pool-4-thread-2] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getFirewallList,458] - 图幻-----获取防火墙列表-----失败： SSLHandshakeException: Remote host terminated the handshake
2025-08-25 10:52:11.456 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4192 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",34639,"**************",3306,"tcp","[2002842] mysql数据库root用户登录尝试","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiMDEiLCAiY29udGVudF9zdHJpbmciOiAiXHUwMDAxIn0sIHsiY29udGVudF9oZXgiOiAiNzI2Z...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2002842,2,"请求","+AAAAY+iOwH///8AIQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcm9vdAAUK3bc0KnaK23dytIkMTpOCIl/VhBwcm9kdWN0X3Bvc...","[]","2025-08-23 06:01:36",1]
2025-08-25 10:52:11.878 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4635 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",34639,"**************",3306,"tcp","[2002842] mysql数据库root用户登录尝试","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiMDEiLCAiY29udGVudF9zdHJpbmciOiAiXHUwMDAxIn0sIHsiY29udGVudF9oZXgiOiAiNzI2Z...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2002842,2,"请求","+AAAAY+iOwH///8AIQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcm9vdAAUK3bc0KnaK23dytIkMTpOCIl/VhBwcm9kdWN0X3Bvc...","[]","2025-08-23 06:01:36",4]
2025-08-25 10:52:14.873 [pool-4-thread-2] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getNatList,492] - 图幻-----获取防火墙NAT列表--失败：SSLHandshakeException: Remote host terminated the handshake
2025-08-25 10:52:20.152 [async-task-pool17] ERROR c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,509] - 处理第1页数据时发生异常，已达最大重试次数: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:987)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1974)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1701)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1679)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:330)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:355)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:324)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:322)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4593)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:447)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$1(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 10:52:20.283 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7138 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",33251,"**************",3306,"tcp","[1300005] 疑似 MySQL 3306 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300005,2,"请求","","[]","2025-08-23 06:00:08",1]
2025-08-25 10:52:20.304 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5256 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",33251,"**************",3306,"tcp","[1300005] 疑似 MySQL 3306 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300005,2,"请求","","[]","2025-08-23 06:00:08",4]
2025-08-25 10:52:20.388 [async-task-pool16] ERROR c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,509] - 处理第1页数据时发生异常，已达最大重试次数: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:987)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1974)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1701)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1679)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:330)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:355)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:324)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:322)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4593)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:447)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$1(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 10:52:22.034 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1460 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",44063,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","+3gBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-23 05:59:02",1]
2025-08-25 10:52:22.140 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1551 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",44063,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","+3gBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-23 05:59:02",4]
2025-08-25 10:52:31.924 [pool-12-thread-4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2566 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-25 10:52:31.943 [pool-12-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2515 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         ) ) and ta.handle_state=0["***************","***************","***************","***************"]
2025-08-25 10:52:32.020 [pool-12-thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2662 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-25 10:52:32.960 [pool-12-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3626 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) ) and ta.handle_state=0["**************","***************","***************","**************","***************","***************"]
2025-08-25 10:52:35.004 [async-task-pool26] ERROR c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,509] - 处理第1页数据时发生异常，已达最大重试次数: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:987)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1974)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1701)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1679)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:330)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:355)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:324)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:322)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4593)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:447)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$1(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 10:52:35.005 [async-task-pool27] ERROR c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,509] - 处理第1页数据时发生异常，已达最大重试次数: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:987)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1974)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1701)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1679)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:330)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:355)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:324)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:322)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4593)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:447)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$1(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 11:04:38.795 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1107 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",42032,"*************",6379,"tcp","[1300018] 疑似 Redis 6379 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300018,2,"请求","","[]","2025-08-23 13:14:48",4]
2025-08-25 11:04:39.138 [async-task-pool5] ERROR c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,509] - 处理第1页数据时发生异常，已达最大重试次数: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:987)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1974)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1701)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1679)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:330)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:355)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:324)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:322)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4593)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:447)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$1(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 11:04:39.193 [async-task-pool4] ERROR c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,509] - 处理第1页数据时发生异常，已达最大重试次数: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:987)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1974)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1701)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1679)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:330)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:355)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:324)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:322)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4593)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:447)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$1(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 11:04:41.503 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2195 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",49826,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","dWQBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAAcAAE=","[]","2025-08-23 13:14:39",4]
2025-08-25 11:04:42.853 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1271 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["************",42172,"***************",82,"http","[1201022] 请求中包含扫描脚本特征_HEADER","其他/web后门","W3sicGNyZSI6ICIobm1hcFxccytzY3JpcHRpbmd8bnVjbGVpXFwuc3ZnfGh0dHBzOi8vd3d3XFwuZ29vZ2xlXFwuY29tL3NlY...",405,"SFRUUC8xLjEgNDA1CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1201022,3,"请求","UE9TVCAvc2RrIEhUVFAvMS4xDQpIb3N0OiAxNzEuMzQuMjA2LjU6MTgwODANCkNvbnRlbnQtTGVuZ3RoOiA0NDENClVzZXItQ...","[]","2025-08-23 13:14:38",4]
2025-08-25 11:04:46.339 [pool-4-thread-2] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getFirewallList,458] - 图幻-----获取防火墙列表-----失败： SSLHandshakeException: Remote host terminated the handshake
2025-08-25 11:04:50.655 [async-task-pool0] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13375 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",38158,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","NQEBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAAcAAE=","[]","2025-08-23 13:34:11",1]
2025-08-25 11:04:51.465 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8523 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",45686,"**************",22,"tcp","[2001219] 疑似 SSH 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",2001219,2,"请求","","[]","2025-08-23 13:14:15",4]
2025-08-25 11:04:55.185 [pool-4-thread-2] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getNatList,492] - 图幻-----获取防火墙NAT列表--失败：SSLHandshakeException: Remote host terminated the handshake
2025-08-25 11:04:56.021 [async-task-pool17] ERROR c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,509] - 处理第1页数据时发生异常，已达最大重试次数: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:987)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1974)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1701)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1679)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:330)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:355)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:324)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:322)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4593)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:447)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$1(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 11:04:56.432 [async-task-pool16] ERROR c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,509] - 处理第1页数据时发生异常，已达最大重试次数: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:987)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1974)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1701)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1679)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:330)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:355)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:324)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:322)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4593)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:447)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$1(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 11:04:58.040 [async-task-pool0] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4004 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",55694,"*************",6379,"tcp","[1300018] 疑似 Redis 6379 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300018,2,"请求","","[]","2025-08-23 13:33:50",1]
2025-08-25 11:05:04.092 [async-task-pool0] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5900 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",47175,"*******",53,"dns","[1621883] 僵尸木马事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621883,4,"请求","tikBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAAcAAE=","[]","2025-08-23 13:33:11",1]
2025-08-25 11:05:04.992 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9998 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",51837,"*******",53,"dns","[1621883] 僵尸木马事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621883,4,"请求","G38BAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-23 13:13:14",4]
2025-08-25 11:05:12.552 [async-task-pool25] ERROR c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,509] - 处理第1页数据时发生异常，已达最大重试次数: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:987)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1974)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1701)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1679)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:330)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:355)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:324)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:322)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4593)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:447)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$1(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 11:05:12.607 [async-task-pool24] ERROR c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,509] - 处理第1页数据时发生异常，已达最大重试次数: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "exposure_internet" (class com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail), not marked as ignorable (7 known properties: "state", "risk_assets", "engine_name", "risk_type", "risk_info", "update_time", "start_time"])
 at [Source: (String)"{"alert": [{"exposure_internet": "\u5426", "risk_assets": "**************", "risk_type": "\u5f31\u53e3\u4ee4\u8d26\u53f7", "risk_info": "['\u8d26\u53f7:ftpuser, \u5bc6\u7801:******']", "engine_name": "********", "start_time": "2024-10-31 11:52:33", "update_time": "2025-08-25 10:33:49", "state": "\u672a\u5904\u7f6e"}, {"exposure_internet": "\u5426", "risk_assets": "http://twx.hyite.cn/h5_rest/v1/loginStep2", "risk_type": "\u654f\u611f\u4fe1\u606fAPI-\u8eab\u4efd\u8bc1", "risk_info": "['36********"[truncated 719 chars]; line: 1, column: 35] (through reference chain: com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult["alert"]->java.util.ArrayList[0]->com.ruoyi.ffsafe.api.domain.FlowRiskAssetsResult$FlowRiskAssetDetail["exposure_internet"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:987)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1974)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1701)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1679)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:330)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:355)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:324)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:322)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4593)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:447)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$1(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-25 11:05:13.266 [async-task-pool0] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2381 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",35816,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","VCABAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-23 13:32:09",1]
2025-08-25 11:05:13.272 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2868 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",38877,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","zBIBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAAcAAE=","[]","2025-08-23 13:12:02",4]
2025-08-25 11:05:14.451 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1007 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",47061,"*******",53,"dns","[1621883] 僵尸木马事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621883,4,"请求","4jwBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-23 13:11:06",4]
2025-08-25 11:05:14.456 [async-task-pool0] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1013 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",35178,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","Ap4BAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAAcAAE=","[]","2025-08-23 13:30:57",1]
2025-08-25 11:05:16.349 [taskScheduler-5] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - [handleError,95] - Unexpected error occurred in scheduled task
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.ruoyi.safe.task.AssetThreatsNumTask.countAssetThreatsNum(AssetThreatsNumTask.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
