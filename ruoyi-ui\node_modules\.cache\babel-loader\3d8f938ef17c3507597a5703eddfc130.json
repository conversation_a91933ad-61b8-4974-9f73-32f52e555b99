{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\layout\\components\\Sidebar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1756090207111}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vuex", "require", "_Logo", "_interopRequireDefault", "_SidebarItem", "_variables2", "components", "SidebarItem", "Logo", "mounted", "computed", "_objectSpread2", "default", "mapState", "mapGetters", "activeMenu", "route", "$route", "meta", "path", "parts", "split", "length", "concat", "showLogo", "$store", "state", "settings", "sidebarLogo", "variables", "isCollapse", "sidebar", "opened"], "sources": ["src/layout/components/Sidebar/index.vue"], "sourcesContent": ["<template>\n  <div :class=\"{'has-logo':showLogo}\">\n<!--       :style=\"{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground,-->\n<!--           background: settings.sideTheme === 'theme-dark' ? '' : variables.menuGradient }\">-->\n<!--    <logo v-if=\"showLogo\" :collapse=\"isCollapse\"/>-->\n    <el-scrollbar :class=\"settings.sideTheme\" wrap-class=\"scrollbar-wrapper\">\n      <!--          :background-color=\"settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground\"-->\n      <el-menu\n        :default-active=\"activeMenu\"\n        :collapse=\"isCollapse\"\n        :unique-opened=\"true\"\n        :collapse-transition=\"false\"\n        mode=\"vertical\"\n      >\n        <sidebar-item\n          v-for=\"(route, index) in sidebarRouters\"\n          :key=\"route.path  + index\"\n          :item=\"route\"\n          :base-path=\"route.path\"\n        />\n      </el-menu>\n    </el-scrollbar>\n    <div class=\"sidebar-bottom\" v-if=\"!isCollapse\">\n      <div class=\"text\">\n        <p><span>当前版本：2.5.0</span><span style=\"display: none;\">Date: 2025-04-27 17:24 SvnVersion: $Revision: 2884 $</span></p>\n        <p><span>技术支持：安盟科技</span></p>\n        <p><span>联系方式：4001-110-9963</span></p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {mapGetters, mapState} from \"vuex\";\nimport Logo from \"./Logo\";\nimport SidebarItem from \"./SidebarItem\";\nimport variables from \"@/assets/styles/variables.scss\";\n\nexport default {\n  components: {SidebarItem, Logo},\n  mounted() {},\n  computed: {\n    ...mapState([\"settings\"]),\n    ...mapGetters([\"sidebarRouters\", \"sidebar\"]),\n    activeMenu() {\n      const route = this.$route;\n      const {meta, path} = route;\n      // if set path, the sidebar will highlight the path you set\n      if (meta.activeMenu) {\n        return meta.activeMenu;\n      }\n      let activeMenu\n      const parts = path.split('/');\n      if (parts.length === 4 && parts[1] && parts[2] && parts[3]) {\n        activeMenu = `/${parts[1]}/${parts[2]}`;\n      }else{\n        activeMenu =  path;\n      }\n      return activeMenu\n    },\n    showLogo() {\n      return this.$store.state.settings.sidebarLogo;\n    },\n    variables() {\n      return variables;\n    },\n    // 侧边栏折叠状态\n    isCollapse() {\n      return !this.sidebar.opened;\n    }\n  }\n};\n</script>\n<style scoped lang=\"scss\">\n.el-menu {\n  background-color: unset;\n  ::v-deep .el-submenu__title i {\n    font-weight: 700;\n  }\n}\n.sidebar-bottom {\n  width: 190px;\n  height: 72px;\n  position: fixed;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  border-top: 1px solid #7f7f7f24;\n  .text{\n    position: absolute;\n    text-align: center;\n    padding: 2px 2px 2px 2px;\n    box-sizing: border-box;\n    width: 100%;\n    height: 54px;\n    color: rgba(127, 127, 127, 0.4980392156862745);\n    font-size: 12px;\n    word-wrap: break-word;\n    text-transform: none;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;AAiCA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,UAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,IAAA,EAAAA;EAAA;EACAC,OAAA,WAAAA,QAAA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,cAAA,kBACA,IAAAC,gBAAA;IACAC,UAAA,WAAAA,WAAA;MACA,IAAAC,KAAA,QAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,KAAA,CAAAE,IAAA;QAAAC,IAAA,GAAAH,KAAA,CAAAG,IAAA;MACA;MACA,IAAAD,IAAA,CAAAH,UAAA;QACA,OAAAG,IAAA,CAAAH,UAAA;MACA;MACA,IAAAA,UAAA;MACA,IAAAK,KAAA,GAAAD,IAAA,CAAAE,KAAA;MACA,IAAAD,KAAA,CAAAE,MAAA,UAAAF,KAAA,OAAAA,KAAA,OAAAA,KAAA;QACAL,UAAA,OAAAQ,MAAA,CAAAH,KAAA,UAAAG,MAAA,CAAAH,KAAA;MACA;QACAL,UAAA,GAAAI,IAAA;MACA;MACA,OAAAJ,UAAA;IACA;IACAS,QAAA,WAAAA,SAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,WAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,OAAAA,mBAAA;IACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,aAAAC,OAAA,CAAAC,MAAA;IACA;EAAA;AAEA", "ignoreList": []}]}